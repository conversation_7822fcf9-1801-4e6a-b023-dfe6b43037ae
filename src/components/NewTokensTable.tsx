'use client';

import { useState, useMemo } from 'react';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

interface NewTokensTableProps {
  tokens: TokenInfo[];
  className?: string;
}

export default function NewTokensTable({ tokens, className = '' }: NewTokensTableProps) {
  const [sortBy, setSortBy] = useState<'timestamp' | 'name' | 'symbol'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Filter only newly minted tokens (no market data yet)
  const newTokens = useMemo(() => {
    return tokens.filter(token => {
      // Token is newly discovered without market data yet
      return !(
        (token.marketCap !== undefined && token.marketCap > 0) ||
        (token.priceSOL !== undefined && token.priceSOL > 0) ||
        (token.priceUSD !== undefined && token.priceUSD > 0) ||
        (token.bondingCurve !== undefined && token.bondingCurve !== '') ||
        token.isGraduated === true
      );
    });
  }, [tokens]);

  // Sort tokens based on current sort settings
  const sortedTokens = useMemo(() => {
    const sorted = [...newTokens];
    sorted.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'timestamp':
          comparison = a.timestamp - b.timestamp;
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'symbol':
          comparison = a.symbol.localeCompare(b.symbol);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    return sorted;
  }, [newTokens, sortBy, sortOrder]);

  const handleSort = (column: 'timestamp' | 'name' | 'symbol') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  const handleImageError = (mint: string) => {
    setImageErrors(prev => new Set([...prev, mint]));
  };

  const getTimeAgo = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) {
      return (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    
    return sortOrder === 'asc' ? (
      <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
      </svg>
    ) : (
      <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    );
  };

  const handleSocialClick = (url: string | undefined, e: React.MouseEvent) => {
    e.stopPropagation();
    if (url) {
      window.open(url, '_blank', 'noopener,noreferrer');
    }
  };

  const copyToClipboard = (text: string, e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(text);
  };

  return (
    <div className={`new-tokens-table ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-cyan-500 rounded-full"></div>
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            Newly Minted Tokens
          </h2>
          <div className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              {sortedTokens.length} tokens
            </span>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Fresh tokens just discovered on PumpFun - no market data available yet
        </p>
      </div>

      {/* Table Container */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-xl border border-gray-200/30 dark:border-gray-700/30 overflow-hidden">
        {sortedTokens.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-4 opacity-50">🆕</div>
              <p className="text-lg font-medium mb-2">No newly minted tokens</p>
              <p className="text-sm opacity-75">
                New tokens will appear here when first detected on PumpFun
              </p>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full min-w-[800px]">
              {/* Table Header */}
              <thead className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white">
                <tr>
                  <th className="px-3 md:px-6 py-4 text-left text-sm font-semibold">Token</th>
                  <th
                    className="px-3 md:px-6 py-4 text-left text-sm font-semibold cursor-pointer hover:bg-white/10 transition-colors"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center gap-2">
                      Name
                      {getSortIcon('name')}
                    </div>
                  </th>
                  <th
                    className="px-3 md:px-6 py-4 text-left text-sm font-semibold cursor-pointer hover:bg-white/10 transition-colors hidden sm:table-cell"
                    onClick={() => handleSort('symbol')}
                  >
                    <div className="flex items-center gap-2">
                      Symbol
                      {getSortIcon('symbol')}
                    </div>
                  </th>
                  <th
                    className="px-3 md:px-6 py-4 text-left text-sm font-semibold cursor-pointer hover:bg-white/10 transition-colors"
                    onClick={() => handleSort('timestamp')}
                  >
                    <div className="flex items-center gap-2">
                      Discovered
                      {getSortIcon('timestamp')}
                    </div>
                  </th>
                  <th className="px-3 md:px-6 py-4 text-left text-sm font-semibold hidden md:table-cell">Social</th>
                  <th className="px-3 md:px-6 py-4 text-left text-sm font-semibold">Actions</th>
                </tr>
              </thead>

              {/* Table Body */}
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {sortedTokens.map((token, index) => (
                  <tr 
                    key={token.mint}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors animate-fade-in"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    {/* Token Image */}
                    <td className="px-3 md:px-6 py-4">
                      <div className="flex items-center">
                        {token.image && !imageErrors.has(token.mint) ? (
                          <img
                            src={token.image}
                            alt={token.name}
                            className="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover"
                            onError={() => handleImageError(token.mint)}
                          />
                        ) : (
                          <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center">
                            <span className="text-xs md:text-sm font-bold text-white">
                              {token.symbol.slice(0, 2).toUpperCase()}
                            </span>
                          </div>
                        )}
                      </div>
                    </td>

                    {/* Token Name */}
                    <td className="px-3 md:px-6 py-4">
                      <div className="font-medium text-gray-900 dark:text-white text-sm md:text-base">
                        {token.name}
                      </div>
                      <div className="sm:hidden">
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 mt-1">
                          ${token.symbol}
                        </span>
                      </div>
                      {token.description && (
                        <div className="text-xs md:text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs mt-1">
                          {token.description}
                        </div>
                      )}
                    </td>

                    {/* Symbol - Hidden on mobile */}
                    <td className="px-3 md:px-6 py-4 hidden sm:table-cell">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                        ${token.symbol}
                      </span>
                    </td>

                    {/* Timestamp */}
                    <td className="px-3 md:px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {getTimeAgo(token.timestamp)}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 hidden md:block">
                        {new Date(token.timestamp).toLocaleTimeString()}
                      </div>
                    </td>

                    {/* Social Links - Hidden on mobile */}
                    <td className="px-3 md:px-6 py-4 hidden md:table-cell">
                      <div className="flex items-center gap-1">
                        {token.twitter && (
                          <button
                            onClick={(e) => handleSocialClick(token.twitter, e)}
                            className="p-1.5 text-gray-400 hover:text-blue-500 transition-colors rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            title="Twitter"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                          </button>
                        )}
                        {token.telegram && (
                          <button
                            onClick={(e) => handleSocialClick(token.telegram, e)}
                            className="p-1.5 text-gray-400 hover:text-blue-500 transition-colors rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            title="Telegram"
                          >
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                            </svg>
                          </button>
                        )}
                        {token.website && (
                          <button
                            onClick={(e) => handleSocialClick(token.website, e)}
                            className="p-1.5 text-gray-400 hover:text-blue-500 transition-colors rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            title="Website"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-3 md:px-6 py-4">
                      <div className="flex items-center gap-1 md:gap-2 flex-col md:flex-row">
                        <button
                          onClick={(e) => copyToClipboard(token.mint, e)}
                          className="px-2 md:px-3 py-1 md:py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors w-full md:w-auto"
                          title="Copy mint address"
                        >
                          Mint
                        </button>
                        <button
                          onClick={(e) => copyToClipboard(token.signature, e)}
                          className="px-2 md:px-3 py-1 md:py-1.5 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors w-full md:w-auto"
                          title="Copy transaction signature"
                        >
                          Tx
                        </button>
                        {/* Social links for mobile */}
                        <div className="flex items-center gap-1 md:hidden mt-1">
                          {token.twitter && (
                            <button
                              onClick={(e) => handleSocialClick(token.twitter, e)}
                              className="p-1 text-gray-400 hover:text-blue-500 transition-colors rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20"
                              title="Twitter"
                            >
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                              </svg>
                            </button>
                          )}
                          {token.telegram && (
                            <button
                              onClick={(e) => handleSocialClick(token.telegram, e)}
                              className="p-1 text-gray-400 hover:text-blue-500 transition-colors rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20"
                              title="Telegram"
                            >
                              <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                              </svg>
                            </button>
                          )}
                          {token.website && (
                            <button
                              onClick={(e) => handleSocialClick(token.website, e)}
                              className="p-1 text-gray-400 hover:text-blue-500 transition-colors rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20"
                              title="Website"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 919-9" />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Footer Stats */}
      {sortedTokens.length > 0 && (
        <div className="mt-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center justify-center gap-6 text-sm text-gray-600 dark:text-gray-300">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>{sortedTokens.length} newly minted tokens</span>
            </div>
            <div className="text-gray-400">•</div>
            <span>Updated in real-time</span>
          </div>
        </div>
      )}
    </div>
  );
}
